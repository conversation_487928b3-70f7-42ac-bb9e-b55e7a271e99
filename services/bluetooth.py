from typing import List
from fabric.bluetooth import BluetoothClient
from fabric.core.service import Property, Service, Signal
from loguru import logger


class BluetoothService(Service):
    """A service to manage bluetooth functionality"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @Signal
    def changed(self) -> None: ...

    @Signal
    def device_added(self, address: str) -> None: ...

    @Signal
    def device_removed(self, address: str) -> None: ...

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._client = BluetoothClient()
        
        # Connect to client signals
        self._client.connect("changed", self.on_client_changed)
        self._client.connect("device-added", self.on_device_added)
        self._client.connect("device-removed", self.on_device_removed)

    def on_client_changed(self, *args):
        """Handle bluetooth client changes"""
        self.emit("changed")
        # Notify all properties that might have changed
        for prop in ["enabled", "connected_devices", "icon_name"]:
            self.notify(prop)

    def on_device_added(self, client, address):
        """Handle device added"""
        self.emit("device-added", address)
        self.emit("changed")

    def on_device_removed(self, client, address):
        """Handle device removed"""
        self.emit("device-removed", address)
        self.emit("changed")

    def toggle_bluetooth(self):
        """Toggle bluetooth on/off"""
        try:
            self._client.enabled = not self._client.enabled
        except Exception as e:
            logger.error(f"[BluetoothService] Failed to toggle bluetooth: {e}")

    @Property(bool, "read-write", default_value=False)
    def enabled(self) -> bool:
        """Get bluetooth enabled state"""
        return self._client.enabled if self._client else False

    @enabled.setter
    def enabled(self, value: bool):
        """Set bluetooth enabled state"""
        if self._client:
            self._client.enabled = value

    @Property(object, "readable")
    def connected_devices(self) -> List:
        """Get list of connected bluetooth devices"""
        return self._client.connected_devices if self._client else []

    @Property(object, "readable")
    def devices(self) -> List:
        """Get list of all bluetooth devices"""
        return self._client.devices if self._client else []

    @Property(str, "readable")
    def icon_name(self) -> str:
        """Get appropriate icon name based on bluetooth state"""
        if not self.enabled:
            return "bluetooth-disabled-symbolic"
        
        connected_devices = self.connected_devices
        if connected_devices:
            return "bluetooth-active-symbolic"
        else:
            return "bluetooth-symbolic"

    @Property(str, "readable")
    def status_text(self) -> str:
        """Get status text for tooltip"""
        if not self.enabled:
            return "Bluetooth disabled"
        
        connected_devices = self.connected_devices
        if not connected_devices:
            return "No devices connected"
        
        if len(connected_devices) == 1:
            device = connected_devices[0]
            text = f"Connected to {device.alias}"
            if hasattr(device, 'battery_percentage') and device.battery_percentage > 0:
                text += f" ({device.battery_percentage:.0f}%)"
            return text
        else:
            return f"Connected to {len(connected_devices)} devices"

    def connect_device(self, address: str) -> bool:
        """Connect to a bluetooth device"""
        try:
            device = next((d for d in self.devices if d.address == address), None)
            if device:
                device.connect()
                return True
            return False
        except Exception as e:
            logger.error(f"[BluetoothService] Failed to connect to device {address}: {e}")
            return False

    def disconnect_device(self, address: str) -> bool:
        """Disconnect from a bluetooth device"""
        try:
            device = next((d for d in self.connected_devices if d.address == address), None)
            if device:
                device.disconnect()
                return True
            return False
        except Exception as e:
            logger.error(f"[BluetoothService] Failed to disconnect from device {address}: {e}")
            return False

    def pair_device(self, address: str) -> bool:
        """Pair with a bluetooth device"""
        try:
            device = next((d for d in self.devices if d.address == address), None)
            if device:
                device.pair()
                return True
            return False
        except Exception as e:
            logger.error(f"[BluetoothService] Failed to pair with device {address}: {e}")
            return False
