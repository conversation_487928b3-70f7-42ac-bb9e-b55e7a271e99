#!/usr/bin/env python3

import gi
gi.require_version('Gtk', '3.0')
from gi.repository import Gtk, GLib

from modules.panel.components.indicators import NetworkIndicator
from utils.service import modus_service

def test_network_indicator():
    print("Testing NetworkIndicator widget with modus_service integration...")
    
    # Create a simple window to test the network indicator
    window = Gtk.Window()
    window.set_title("Network Indicator Test")
    window.set_default_size(300, 100)
    window.connect("destroy", Gtk.main_quit)
    
    # Create the network indicator
    network_indicator = NetworkIndicator()
    
    # Add some padding around the indicator
    box = Gtk.Box(orientation=Gtk.Orientation.HORIZONTAL)
    box.set_margin_top(20)
    box.set_margin_bottom(20)
    box.set_margin_left(20)
    box.set_margin_right(20)
    box.pack_start(network_indicator, True, True, 0)
    
    window.add(box)
    window.show_all()
    
    # Print initial wlan state
    print(f"Initial wlan state: {modus_service.wlan}")
    
    # Monitor wlan changes
    def on_wlan_changed(service, new_state):
        print(f"WLAN state changed to: {new_state}")
    
    modus_service.connect("wlan-changed", on_wlan_changed)
    
    # Auto-close after 10 seconds for testing
    GLib.timeout_add(10000, Gtk.main_quit)
    
    print("Window should be visible now...")
    Gtk.main()

if __name__ == "__main__":
    test_network_indicator()
