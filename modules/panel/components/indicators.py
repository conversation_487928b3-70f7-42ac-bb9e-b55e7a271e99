from fabric.bluetooth import BluetoothClient
from fabric.utils import get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.svg import Svg
from services.network import NetworkService
from utils.service import modus_service


class BluetoothIndicator(Box):
    def __init__(self, **kwargs):
        super().__init__(
            name="bluetooth-indicator",
            orientation="h",
            **kwargs
        )

        self.bluetooth = BluetoothClient()
        self.bt_icon = Svg(
            name="bt-icon",
            size=20,
            svg_file=get_relative_path("../../../config/assets/icons/bluetooth.svg"),
        )

        self.bt_button = Button(
            name="bt-button",
            child=self.bt_icon
        )

        self.add(self.bt_button)

        modus_service.connect("bluetooth-changed", self.on_bluetooth_changed)
        self.bluetooth.connect("changed", self.on_bluetooth_direct_changed)
        self.bluetooth.connect("device-added", self.on_device_added)
        self.bluetooth.connect("device-removed", self.on_device_removed)

        # Initialize modus_service bluetooth state
        self.update_modus_service_bluetooth_state()
        self.update_state()

    def update_state(self):
        if not self.bluetooth.enabled:
            self.bt_icon.set_from_file(
                get_relative_path("../../../config/assets/icons/bluetooth-off.svg")
            )
            tooltip = "Bluetooth disabled"
        else:
            connected_devices = self.bluetooth.connected_devices
            if connected_devices:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                if len(connected_devices) == 1:
                    device = connected_devices[0]
                    tooltip = f"Connected to {device.alias}"
                    if device.battery_percentage > 0:
                        tooltip += f" ({device.battery_percentage:.0f}%)"
                else:
                    tooltip = f"Connected to {len(connected_devices)} devices"
            else:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                tooltip = "No devices connected"

        self.bt_button.set_tooltip_text(tooltip)

    def on_bluetooth_changed(self, service, new_bluetooth_state):
        self.update_state()

    def on_bluetooth_direct_changed(self, *args):
        self.update_modus_service_bluetooth_state()
        self.update_state()

    def on_device_added(self, _, address):
        self.update_modus_service_bluetooth_state()
        self.update_state()

    def on_device_removed(self, _, address):
        self.update_modus_service_bluetooth_state()
        self.update_state()

    def update_modus_service_bluetooth_state(self):
        if not self.bluetooth.enabled:
            bluetooth_state = "disabled"
        else:
            connected_devices = self.bluetooth.connected_devices
            if connected_devices:
                if len(connected_devices) == 1:
                    device = connected_devices[0]
                    bluetooth_state = f"connected:{device.alias}"
                    if hasattr(device, 'battery_percentage') and device.battery_percentage > 0:
                        bluetooth_state += f":{device.battery_percentage:.0f}%"
                else:
                    bluetooth_state = f"connected:{len(connected_devices)}_devices"
            else:
                bluetooth_state = "enabled"

        modus_service.bluetooth = bluetooth_state


class NetworkIndicator(Box):
    def __init__(self, **kwargs):
        super().__init__(
            name="network-indicator",
            orientation="h",
            **kwargs
        )

        self.network_service = NetworkService()

        self.network_icon = Svg(
            name="network-icon",
            size=20,
            svg_file=get_relative_path("../../../config/assets/icons/wifi.svg"),
        )

        self.network_button = Button(
            name="network-button",
            child=self.network_icon
        )

        self.add(self.network_button)

        self.network_service.connect("device-ready", self.on_device_ready)

        self.update_state()

    def on_device_ready(self, *args):
        if self.network_service.wifi_device:
            self.network_service.wifi_device.connect("changed", self.on_network_changed)

        if self.network_service.ethernet_device:
            self.network_service.ethernet_device.connect("changed", self.on_network_changed)

        self.update_state()

    def on_network_changed(self, *args):
        self.update_state()

    def update_state(self):
        primary_device = self.network_service.primary_device
        tooltip = "No network connection"
        icon_file = "wifi-off.svg"

        if primary_device == "wifi" and self.network_service.wifi_device:
            wifi = self.network_service.wifi_device
            if not wifi.enabled:
                icon_file = "wifi-off.svg"
                tooltip = "WiFi disabled"
            elif wifi.internet == "activated":
                icon_file = "wifi.svg"
                tooltip = f"Connected to {wifi.ssid}"
                if wifi.strength >= 0:
                    tooltip += f" ({wifi.strength}%)"
            elif wifi.internet == "activating":
                icon_file = "wifi.svg"
                tooltip = f"Connecting to {wifi.ssid}..."
            else:
                icon_file = "wifi-off.svg"
                tooltip = "WiFi disconnected"

        elif primary_device == "wired" and self.network_service.ethernet_device:
            ethernet = self.network_service.ethernet_device
            if ethernet.internet == "activated":
                icon_file = "ethernet.svg"
                tooltip = "Ethernet connected"
                if hasattr(ethernet, 'speed') and ethernet.speed > 0:
                    tooltip += f" ({ethernet.speed} Mbps)"
            elif ethernet.internet == "activating":
                icon_file = "wifi.svg"
                tooltip = "Ethernet connecting..."
            else:
                icon_file = "wifi-off.svg"
                tooltip = "Ethernet disconnected"

        # Update icon and tooltip
        self.network_icon.set_from_file(
            get_relative_path(f"../../../config/assets/icons/{icon_file}")
        )
        self.network_button.set_tooltip_text(tooltip)




