from fabric.bluetooth import BluetoothClient
from fabric.utils import get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.label import Label
from fabric.widgets.svg import Svg


class BluetoothIndicator(Box):
    def __init__(self, **kwargs):
        super().__init__(
            name="bluetooth-indicator",
            orientation="h",
            **kwargs
        )

        self.bluetooth = BluetoothClient()

        # Use SVG icons instead of font icons
        self.bt_icon = Svg(
            name="bt-icon",
            size=20,
            svg_file=get_relative_path("../../../config/assets/icons/bluetooth.svg"),
        )

        # Wrap the icon in a button for click functionality
        self.bt_button = Button(
            name="bt-button",
            child=self.bt_icon
        )

        self.add(self.bt_button)

        self.bluetooth.connect("changed", self.on_bluetooth_changed)
        self.bluetooth.connect("device-added", self.on_device_added)
        self.bluetooth.connect("device-removed", self.on_device_removed)

        self.update_state()

    def update_state(self):
        if not self.bluetooth.enabled:
            self.bt_icon.set_from_file(
                get_relative_path("../../../config/assets/icons/bluetooth-off.svg")
            )
            tooltip = "Bluetooth disabled"
        else:
            connected_devices = self.bluetooth.connected_devices
            if connected_devices:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                if len(connected_devices) == 1:
                    device = connected_devices[0]
                    tooltip = f"Connected to {device.alias}"
                    if device.battery_percentage > 0:
                        tooltip += f" ({device.battery_percentage:.0f}%)"
                else:
                    tooltip = f"Connected to {len(connected_devices)} devices"
            else:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                tooltip = "No devices connected"

        self.bt_button.set_tooltip_text(tooltip)

    def on_bluetooth_changed(self, *args):
        self.update_state()

    def on_device_added(self, _, address):
        self.update_state()

    def on_device_removed(self, _, address):
        self.update_state()



# class Indicators(Box):
#     def __init__(self, **kwargs):
#         super().__init__(
#             name="indicators",
#             orientation="h",
#             spacing=10,
#             children=[
#                 BluetoothIndicator(),
#             ],
#             **kwargs,
#         )
#         self.show_all()
