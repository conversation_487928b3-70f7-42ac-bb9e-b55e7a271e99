#!/usr/bin/env python3

import gi
gi.require_version('Gtk', '3.0')
from gi.repository import Gtk, GLib

from modules.panel.components.indicators import BluetoothIndicator
from utils.service import modus_service

def test_bluetooth_indicator():
    print("Testing BluetoothIndicator widget...")
    
    # Create a simple window to test the bluetooth indicator
    window = Gtk.Window()
    window.set_title("Bluetooth Indicator Test")
    window.set_default_size(300, 100)
    window.connect("destroy", Gtk.main_quit)
    
    # Create the bluetooth indicator
    bluetooth_indicator = BluetoothIndicator()
    
    # Add some padding around the indicator
    box = Gtk.Box(orientation=Gtk.Orientation.HORIZONTAL)
    box.set_margin_top(20)
    box.set_margin_bottom(20)
    box.set_margin_left(20)
    box.set_margin_right(20)
    box.pack_start(bluetooth_indicator, True, True, 0)
    
    window.add(box)
    window.show_all()
    
    # Print initial bluetooth state
    print(f"Initial bluetooth state: {modus_service.bluetooth}")
    
    # Monitor bluetooth changes
    def on_bluetooth_changed(service, new_state):
        print(f"Bluetooth state changed to: {new_state}")
    
    modus_service.connect("bluetooth-changed", on_bluetooth_changed)
    
    # Auto-close after 10 seconds for testing
    GLib.timeout_add(10000, Gtk.main_quit)
    
    print("Window should be visible now...")
    Gtk.main()

if __name__ == "__main__":
    test_bluetooth_indicator()
