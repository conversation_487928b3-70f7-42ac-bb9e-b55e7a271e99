#!/usr/bin/env python3

import gi
gi.require_version('Gtk', '3.0')
from gi.repository import Gtk, GLib

from modules.panel.components.indicators import NetworkIndicator, BluetoothIndicator, BatteryIndicator
from utils.service import modus_service

def test_all_indicators():
    print("Testing all indicators with modus_service integration...")
    
    # Create a simple window to test all indicators
    window = Gtk.Window()
    window.set_title("All Indicators Test")
    window.set_default_size(400, 100)
    window.connect("destroy", Gtk.main_quit)
    
    # Create all indicators
    network_indicator = NetworkIndicator()
    bluetooth_indicator = BluetoothIndicator()
    battery_indicator = BatteryIndicator()
    
    # Add them to a horizontal box with spacing
    indicators_box = Gtk.Box(orientation=Gtk.Orientation.HORIZONTAL, spacing=10)
    indicators_box.pack_start(network_indicator, False, False, 0)
    indicators_box.pack_start(bluetooth_indicator, False, False, 0)
    indicators_box.pack_start(battery_indicator, False, False, 0)
    
    # Add padding around the indicators
    main_box = Gtk.Box(orientation=Gtk.Orientation.HORIZONTAL)
    main_box.set_margin_top(20)
    main_box.set_margin_bottom(20)
    main_box.set_margin_left(20)
    main_box.set_margin_right(20)
    main_box.pack_start(indicators_box, True, True, 0)
    
    window.add(main_box)
    window.show_all()
    
    # Print initial states
    print(f"Initial network state: {modus_service.wlan}")
    print(f"Initial bluetooth state: {modus_service.bluetooth}")
    print(f"Initial battery state: {modus_service.battery}")
    
    # Monitor all changes
    def on_wlan_changed(service, new_state):
        print(f"WLAN state changed to: {new_state}")
    
    def on_bluetooth_changed(service, new_state):
        print(f"Bluetooth state changed to: {new_state}")
    
    def on_battery_changed(service, new_state):
        print(f"Battery state changed to: {new_state}")
    
    modus_service.connect("wlan-changed", on_wlan_changed)
    modus_service.connect("bluetooth-changed", on_bluetooth_changed)
    modus_service.connect("battery-changed", on_battery_changed)
    
    # Auto-close after 15 seconds for testing
    GLib.timeout_add(15000, Gtk.main_quit)
    
    print("Window should be visible now with all indicators...")
    Gtk.main()

if __name__ == "__main__":
    test_all_indicators()
