#!/usr/bin/env python3

import gi
gi.require_version('Gtk', '3.0')
from gi.repository import Gtk, GLib

from services.network import NetworkService

def test_network_service():
    print("Testing NetworkService...")
    
    # Create network service
    network_service = NetworkService()
    
    def on_device_ready(*args):
        print("Device ready signal received")
        print(f"Primary device: {network_service.primary_device}")
        
        if network_service.wifi_device:
            wifi = network_service.wifi_device
            print(f"WiFi enabled: {wifi.enabled}")
            print(f"WiFi SSID: {wifi.ssid}")
            print(f"WiFi strength: {wifi.strength}")
            print(f"WiFi internet: {wifi.internet}")
            print(f"WiFi icon: {wifi.icon_name}")
        else:
            print("No WiFi device found")
            
        if network_service.ethernet_device:
            ethernet = network_service.ethernet_device
            print(f"Ethernet internet: {ethernet.internet}")
            print(f"Ethernet icon: {ethernet.icon_name}")
        else:
            print("No Ethernet device found")
            
        # Exit after testing
        GLib.timeout_add(2000, Gtk.main_quit)
    
    # Connect to device ready signal
    network_service.connect("device-ready", on_device_ready)
    
    # Start the main loop to allow async initialization
    print("Starting main loop...")
    Gtk.main()

if __name__ == "__main__":
    test_network_service()
